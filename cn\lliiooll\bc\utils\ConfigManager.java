/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  net.md_5.bungee.api.connection.ProxiedPlayer
 *  net.md_5.bungee.config.Configuration
 *  net.md_5.bungee.config.ConfigurationProvider
 *  net.md_5.bungee.config.YamlConfiguration
 */
package cn.lliiooll.bc.utils;

import cn.lliiooll.bc.PKChatRecord;
import cn.lliiooll.bc.data.PlayerChatDataOuterClass;
import cn.lliiooll.bc.utils.FileUtils;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.CopyOption;
import java.nio.file.Files;
import java.util.ArrayList;
import net.md_5.bungee.api.connection.ProxiedPlayer;
import net.md_5.bungee.config.Configuration;
import net.md_5.bungee.config.ConfigurationProvider;
import net.md_5.bungee.config.YamlConfiguration;

public class ConfigManager {
    public static void init() {
        File dir;
        File dataCfgFile;
        File file;
        PKChatRecord instance = PKChatRecord.INSTANCE;
        if (!instance.getDataFolder().exists()) {
            instance.getDataFolder().mkdir();
        }
        if (!(file = new File(instance.getDataFolder(), "config.yml")).exists()) {
            try (InputStream in = instance.getResourceAsStream("config.yml");){
                Files.copy(in, file.toPath(), new CopyOption[0]);
            }
            catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (!(dataCfgFile = new File(instance.getDataFolder(), "players.yml")).exists()) {
            try {
                dataCfgFile.createNewFile();
            }
            catch (IOException e) {
                e.printStackTrace();
            }
        }
        if (!(dir = new File(PKChatRecord.INSTANCE.getDataFolder(), "data")).exists()) {
            dir.mkdirs();
        } else if (dir.listFiles() != null) {
            for (File file1 : dir.listFiles()) {
                file1.renameTo(new File(dir, file.getName().toLowerCase()));
            }
        }
    }

    public static Configuration getDefault() {
        try {
            return ConfigurationProvider.getProvider(YamlConfiguration.class).load(new File(PKChatRecord.INSTANCE.getDataFolder(), "config.yml"));
        }
        catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static void record(String message, ProxiedPlayer player) {
        try {
            String playerName;
            File dataFile;
            File dir = new File(PKChatRecord.INSTANCE.getDataFolder(), "data");
            if (!dir.exists()) {
                dir.mkdirs();
            }
            if (!(dataFile = new File(dir, (playerName = player.getName().toLowerCase()) + ".chat")).exists()) {
                dataFile.createNewFile();
                FileUtils.write(PlayerChatDataOuterClass.PlayerChatData.newBuilder().setName(playerName).setUuid(player.getUniqueId().toString()).addChats(PlayerChatDataOuterClass.PlayerChatData.ChatData.newBuilder().setContent(message).setServer(player.getServer().getInfo().getName()).setTime(System.currentTimeMillis()).build()).build().toByteArray(), dataFile);
            } else {
                PlayerChatDataOuterClass.PlayerChatData data = PlayerChatDataOuterClass.PlayerChatData.parseFrom(FileUtils.read(dataFile));
                ArrayList<PlayerChatDataOuterClass.PlayerChatData.ChatData> chats = new ArrayList<PlayerChatDataOuterClass.PlayerChatData.ChatData>(data.getChatsList());
                chats.add(PlayerChatDataOuterClass.PlayerChatData.ChatData.newBuilder().setContent(message).setTime(System.currentTimeMillis()).setServer(player.getServer().getInfo().getName()).build());
                data = PlayerChatDataOuterClass.PlayerChatData.newBuilder().setUuid(data.getUuid()).setName(data.getName()).addAllChats(chats).build();
                FileUtils.write(data.toByteArray(), dataFile);
            }
        }
        catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    public static PlayerChatDataOuterClass.PlayerChatData getRecord(String playerName) {
        playerName = playerName.toLowerCase();
        File dir = new File(PKChatRecord.INSTANCE.getDataFolder(), "data");
        if (!dir.exists()) {
            return null;
        }
        File dataFile = new File(dir, playerName + ".chat");
        if (!dataFile.exists()) {
            return null;
        }
        try {
            return PlayerChatDataOuterClass.PlayerChatData.parseFrom(FileUtils.read(dataFile));
        }
        catch (Throwable e) {
            e.printStackTrace();
            return null;
        }
    }
}

