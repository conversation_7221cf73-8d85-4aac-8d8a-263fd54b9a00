/*
 * Decompiled with CFR 0.152.
 */
package cn.lliiooll.bc.utils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;

public class FileUtils {
    public static void write(byte[] data, File file) throws Throwable {
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(data);
        fos.close();
    }

    public static byte[] read(File file) throws Throwable {
        FileInputStream fis = new FileInputStream(file);
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        int i = 0;
        while ((i = fis.read()) != -1) {
            bos.write(i);
        }
        fis.close();
        bos.flush();
        bos.close();
        return bos.toByteArray();
    }
}

