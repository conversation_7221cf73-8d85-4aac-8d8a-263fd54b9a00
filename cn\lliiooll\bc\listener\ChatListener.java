/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  net.md_5.bungee.api.connection.ProxiedPlayer
 *  net.md_5.bungee.api.event.ChatEvent
 *  net.md_5.bungee.api.plugin.Listener
 *  net.md_5.bungee.event.EventHandler
 */
package cn.lliiooll.bc.listener;

import cn.lliiooll.bc.utils.ConfigManager;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import net.md_5.bungee.api.connection.ProxiedPlayer;
import net.md_5.bungee.api.event.ChatEvent;
import net.md_5.bungee.api.plugin.Listener;
import net.md_5.bungee.event.EventHandler;

public class ChatListener
implements Listener {
    @EventHandler
    public void onChat(ChatEvent event) {
        if (event.getSender() instanceof ProxiedPlayer) {
            ProxiedPlayer player = (ProxiedPlayer)event.getSender();
            List ignores = ConfigManager.getDefault().getStringList("ignores");
            AtomicBoolean has = new AtomicBoolean(false);
            ignores.forEach(ignore -> {
                if (event.getMessage().startsWith((String)ignore)) {
                    has.set(true);
                }
            });
            if (!has.get()) {
                ConfigManager.record(event.getMessage(), player);
            }
        }
    }
}

